// E-COM Financial Reports System
// JavaScript functionality for enhanced user experience

document.addEventListener('DOMContentLoaded', function() {
    console.log('E-COM Financial Reports System Loaded');
    
    // Initialize all functionality
    initializePrintButtons();
    initializeTableFormatting();
    initializeResponsiveFeatures();
    initializeCurrencyFormatting();
});

// Print functionality
function initializePrintButtons() {
    const printButtons = document.querySelectorAll('button[onclick="window.print()"]');
    printButtons.forEach(button => {
        // Remove inline onclick and add event listener
        button.removeAttribute('onclick');
        button.addEventListener('click', function() {
            window.print();
        });
    });
}

// Format currency numbers with proper Arabic formatting
function formatCurrency(amount) {
    // Convert to number if it's a string
    const num = typeof amount === 'string' ? parseFloat(amount.replace(/,/g, '')) : amount;
    
    // Format with Arabic locale
    return new Intl.NumberFormat('ar-EG', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(num);
}

// Initialize currency formatting for all currency elements
function initializeCurrencyFormatting() {
    const currencyElements = document.querySelectorAll('.currency');
    currencyElements.forEach(element => {
        const text = element.textContent.trim();
        const number = parseFloat(text.replace(/,/g, ''));
        
        if (!isNaN(number)) {
            element.textContent = formatCurrency(number);
        }
    });
}

// Enhanced table formatting
function initializeTableFormatting() {
    const tables = document.querySelectorAll('.financial-table');
    tables.forEach(table => {
        // Add hover effects to rows
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                if (!this.classList.contains('total-row')) {
                    this.style.backgroundColor = '#f8f9fa';
                }
            });
            
            row.addEventListener('mouseleave', function() {
                if (!this.classList.contains('total-row')) {
                    this.style.backgroundColor = '';
                }
            });
        });
    });
}

// Responsive features
function initializeResponsiveFeatures() {
    // Handle mobile navigation
    const navButtons = document.querySelectorAll('.nav-button');
    navButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add loading state for navigation
            if (this.tagName === 'A') {
                this.style.opacity = '0.7';
                this.textContent = 'جاري التحميل...';
            }
        });
    });
    
    // Handle responsive table scrolling
    const tableContainers = document.querySelectorAll('.overflow-x-auto');
    tableContainers.forEach(container => {
        const table = container.querySelector('table');
        if (table && window.innerWidth < 768) {
            // Add scroll indicators for mobile
            addScrollIndicators(container);
        }
    });
}

// Add scroll indicators for mobile tables
function addScrollIndicators(container) {
    const indicator = document.createElement('div');
    indicator.innerHTML = '← اسحب للمشاهدة الكاملة →';
    indicator.style.cssText = `
        text-align: center;
        font-size: 0.875rem;
        color: #6b7280;
        padding: 0.5rem;
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-top: none;
    `;
    
    container.parentNode.insertBefore(indicator, container.nextSibling);
    
    // Hide indicator when scrolled
    container.addEventListener('scroll', function() {
        if (this.scrollLeft > 10) {
            indicator.style.display = 'none';
        } else {
            indicator.style.display = 'block';
        }
    });
}

// Utility function to calculate totals (for validation)
function calculateTotals() {
    const calculations = {
        month3: {
            revenue: 55000,
            expenses: 43696,
            profit: 11304
        },
        month4: {
            revenue: 65000,
            expenses: 51496,
            profit: 13504
        },
        month5: {
            revenue: 62623,
            expenses: 50098,
            profit: 12525
        },
        consolidated: {
            revenue: 182623,
            expenses: 145290,
            profit: 37333
        },
        profitDistribution: {
            drHani: 11200,
            muhammadYoussef: 11200,
            dabour: 14933,
            total: 37333
        }
    };
    
    // Validate calculations
    const isValid = 
        calculations.consolidated.revenue === (calculations.month3.revenue + calculations.month4.revenue + calculations.month5.revenue) &&
        calculations.consolidated.expenses === (calculations.month3.expenses + calculations.month4.expenses + calculations.month5.expenses) &&
        calculations.consolidated.profit === (calculations.month3.profit + calculations.month4.profit + calculations.month5.profit) &&
        calculations.profitDistribution.total === calculations.consolidated.profit;
    
    console.log('Financial calculations validation:', isValid ? 'PASSED' : 'FAILED');
    return calculations;
}

// Export for debugging
window.EComFinancials = {
    formatCurrency,
    calculateTotals,
    initializeCurrencyFormatting
};

// Print-specific functionality
window.addEventListener('beforeprint', function() {
    console.log('Preparing document for printing...');
    
    // Ensure all currency values are properly formatted
    initializeCurrencyFormatting();
    
    // Add print timestamp
    const printInfo = document.createElement('div');
    printInfo.innerHTML = `<p style="font-size: 10pt; color: #666; margin-top: 20px;">تم الطباعة في: ${new Date().toLocaleDateString('ar-EG')}</p>`;
    printInfo.className = 'print-only';
    
    const main = document.querySelector('main');
    if (main) {
        main.appendChild(printInfo);
    }
});

window.addEventListener('afterprint', function() {
    console.log('Print completed');
    
    // Remove print-specific elements
    const printOnly = document.querySelectorAll('.print-only');
    printOnly.forEach(element => element.remove());
});
