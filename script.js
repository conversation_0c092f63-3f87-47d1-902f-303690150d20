// E-COM Financial Reports System
// JavaScript functionality for enhanced user experience

document.addEventListener('DOMContentLoaded', function() {
    console.log('E-COM Financial Reports System Loaded');

    // Initialize all functionality
    initializePrintButtons();
    initializeTableFormatting();
    initializeResponsiveFeatures();
    initializeCurrencyFormatting();

    // Convert all numbers to Arabic and make them bold
    convertAllNumbersToArabicBold();
});

// Print functionality
function initializePrintButtons() {
    const printButtons = document.querySelectorAll('button[onclick="window.print()"]');
    printButtons.forEach(button => {
        // Remove inline onclick and add event listener
        button.removeAttribute('onclick');
        button.addEventListener('click', function() {
            window.print();
        });
    });
}

// Convert English numbers to Arabic numbers
function convertToArabicNumbers(text) {
    const englishToArabic = {
        '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
        '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
    };

    return text.replace(/[0-9]/g, function(match) {
        return englishToArabic[match];
    });
}

// Format currency numbers with proper Arabic formatting and bold
function formatCurrency(amount) {
    // Convert to number if it's a string
    const num = typeof amount === 'string' ? parseFloat(amount.replace(/[,٬]/g, '').replace(/\*\*/g, '')) : amount;

    if (isNaN(num)) return amount;

    // Format with Arabic locale and add thousands separator
    let formatted = new Intl.NumberFormat('ar-EG', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(num);

    // Convert to Arabic numbers and add bold formatting
    formatted = convertToArabicNumbers(formatted);
    return `**${formatted}**`;
}

// Initialize currency formatting for all currency elements
function initializeCurrencyFormatting() {
    const currencyElements = document.querySelectorAll('.currency');
    currencyElements.forEach(element => {
        let text = element.textContent.trim();

        // Skip if already formatted with Arabic numbers
        if (text.includes('**') && /[٠-٩]/.test(text)) {
            return;
        }

        // Extract number from text
        const numberMatch = text.match(/[\d,]+/);
        if (numberMatch) {
            const number = parseFloat(numberMatch[0].replace(/,/g, ''));
            if (!isNaN(number)) {
                element.innerHTML = formatCurrency(number);
            }
        }
    });

    // Process all elements with ** formatting
    processMarkdownBold();
}

// Process ** markdown-style bold formatting
function processMarkdownBold() {
    const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
        if (node.textContent.includes('**')) {
            textNodes.push(node);
        }
    }

    textNodes.forEach(textNode => {
        const text = textNode.textContent;
        const boldRegex = /\*\*(.*?)\*\*/g;

        if (boldRegex.test(text)) {
            const parent = textNode.parentNode;
            const wrapper = document.createElement('span');
            wrapper.innerHTML = text.replace(boldRegex, '<strong>$1</strong>');
            parent.replaceChild(wrapper, textNode);
        }
    });
}

// Enhanced table formatting
function initializeTableFormatting() {
    const tables = document.querySelectorAll('.financial-table');
    tables.forEach(table => {
        // Add hover effects to rows
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                if (!this.classList.contains('total-row')) {
                    this.style.backgroundColor = '#f8f9fa';
                }
            });
            
            row.addEventListener('mouseleave', function() {
                if (!this.classList.contains('total-row')) {
                    this.style.backgroundColor = '';
                }
            });
        });
    });
}

// Responsive features
function initializeResponsiveFeatures() {
    // Handle mobile navigation
    const navButtons = document.querySelectorAll('.nav-button');
    navButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add loading state for navigation
            if (this.tagName === 'A') {
                this.style.opacity = '0.7';
                this.textContent = 'جاري التحميل...';
            }
        });
    });
    
    // Handle responsive table scrolling
    const tableContainers = document.querySelectorAll('.overflow-x-auto');
    tableContainers.forEach(container => {
        const table = container.querySelector('table');
        if (table && window.innerWidth < 768) {
            // Add scroll indicators for mobile
            addScrollIndicators(container);
        }
    });
}

// Add scroll indicators for mobile tables
function addScrollIndicators(container) {
    const indicator = document.createElement('div');
    indicator.innerHTML = '← اسحب للمشاهدة الكاملة →';
    indicator.style.cssText = `
        text-align: center;
        font-size: 0.875rem;
        color: #6b7280;
        padding: 0.5rem;
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-top: none;
    `;
    
    container.parentNode.insertBefore(indicator, container.nextSibling);
    
    // Hide indicator when scrolled
    container.addEventListener('scroll', function() {
        if (this.scrollLeft > 10) {
            indicator.style.display = 'none';
        } else {
            indicator.style.display = 'block';
        }
    });
}

// Utility function to calculate totals (for validation)
function calculateTotals() {
    const calculations = {
        month3: {
            revenue: 55000,
            expenses: 43696,
            profit: 11304
        },
        month4: {
            revenue: 65000,
            expenses: 51496,
            profit: 13504
        },
        month5: {
            revenue: 62623,
            expenses: 50098,
            profit: 12525
        },
        consolidated: {
            revenue: 182623,
            expenses: 145290,
            profit: 37333
        },
        profitDistribution: {
            drHani: 11200,
            muhammadYoussef: 11200,
            dabour: 14933,
            total: 37333
        }
    };
    
    // Validate calculations
    const isValid = 
        calculations.consolidated.revenue === (calculations.month3.revenue + calculations.month4.revenue + calculations.month5.revenue) &&
        calculations.consolidated.expenses === (calculations.month3.expenses + calculations.month4.expenses + calculations.month5.expenses) &&
        calculations.consolidated.profit === (calculations.month3.profit + calculations.month4.profit + calculations.month5.profit) &&
        calculations.profitDistribution.total === calculations.consolidated.profit;
    
    console.log('Financial calculations validation:', isValid ? 'PASSED' : 'FAILED');
    return calculations;
}

// Convert all numbers in the document to Arabic bold format
function convertAllNumbersToArabicBold() {
    // Find all text nodes containing numbers
    const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
        // Skip if already processed or contains Arabic numbers
        if (node.textContent.includes('**') || /[٠-٩]/.test(node.textContent)) {
            continue;
        }

        // Check if contains numbers
        if (/\d/.test(node.textContent)) {
            textNodes.push(node);
        }
    }

    textNodes.forEach(textNode => {
        let text = textNode.textContent;

        // Convert numbers with commas (like 25,000)
        text = text.replace(/\b\d{1,3}(,\d{3})*\b/g, function(match) {
            const arabicNumber = convertToArabicNumbers(match);
            return `**${arabicNumber}**`;
        });

        // Convert standalone numbers
        text = text.replace(/\b\d+\b/g, function(match) {
            const arabicNumber = convertToArabicNumbers(match);
            return `**${arabicNumber}**`;
        });

        if (text !== textNode.textContent) {
            const parent = textNode.parentNode;
            const wrapper = document.createElement('span');
            wrapper.innerHTML = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            parent.replaceChild(wrapper, textNode);
        }
    });
}

// Export for debugging
window.EComFinancials = {
    formatCurrency,
    calculateTotals,
    initializeCurrencyFormatting,
    convertAllNumbersToArabicBold,
    convertToArabicNumbers
};

// Print-specific functionality
window.addEventListener('beforeprint', function() {
    console.log('Preparing document for printing...');
    
    // Ensure all currency values are properly formatted
    initializeCurrencyFormatting();
    
    // Add print timestamp with Arabic date
    const printInfo = document.createElement('div');
    const currentDate = new Date();
    const arabicDate = currentDate.toLocaleDateString('ar-EG');
    const arabicYear = convertToArabicNumbers(currentDate.getFullYear().toString());
    printInfo.innerHTML = `<p style="font-size: 10pt; color: #666; margin-top: 20px;">تم الطباعة في: ${arabicDate} - © <strong>${arabicYear}</strong> شركة E-COM</p>`;
    printInfo.className = 'print-only';
    
    const main = document.querySelector('main');
    if (main) {
        main.appendChild(printInfo);
    }
});

window.addEventListener('afterprint', function() {
    console.log('Print completed');
    
    // Remove print-specific elements
    const printOnly = document.querySelectorAll('.print-only');
    printOnly.forEach(element => element.remove());
});
