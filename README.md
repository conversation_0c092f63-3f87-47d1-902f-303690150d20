# E-COM Financial Reports System

نظام التقارير المالية لشركة E-COM - تطبيق ويب شامل لعرض التقارير المالية باللغة العربية

## المميزات

### التقارير المالية
- قائمة الدخل للشهر الثالث
- قائمة الدخل للشهر الرابع  
- قائمة الدخل للشهر الخامس
- قائمة الدخل الموحدة (3 أشهر)
- قائمة توزيع الأرباح

### المميزات التقنية
- تصميم متجاوب (Responsive Design)
- دعم كامل للغة العربية (RTL)
- خط IBM Plex Sans Arabic
- تصميم احترافي بالأبيض والأسود
- قابلية الطباعة
- تحسين للهواتف المحمولة

## البيانات المالية

### الملخص المالي (3 أشهر)
- **إجمالي الإيرادات:** 182,623 جنيه مصري
- **إجمالي المصروفات:** 145,290 جنيه مصري
- **صافي الربح:** 37,333 جنيه مصري

### توزيع الأرباح
- **د. هاني:** 11,200 جنيه (30%)
- **الأستاذ محمد يوسف:** 11,200 جنيه (30%)
- **الأستاذ دبور:** 14,933 جنيه (40%)

## ملفات المشروع

```
accounting/
├── index.html              # الصفحة الرئيسية
├── month3.html            # قائمة الدخل - الشهر الثالث
├── month4.html            # قائمة الدخل - الشهر الرابع
├── month5.html            # قائمة الدخل - الشهر الخامس
├── consolidated.html      # قائمة الدخل الموحدة
├── profit-distribution.html # قائمة توزيع الأرباح
├── style.css              # ملف التنسيق الرئيسي
├── script.js              # ملف JavaScript للتفاعل
└── README.md              # هذا الملف
```

## التثبيت على Hostinger

### الخطوات
1. **تحميل الملفات:**
   - قم بتحميل جميع الملفات إلى مجلد `public_html` في حساب Hostinger
   - تأكد من رفع الملفات التالية:
     - `index.html`
     - `month3.html`
     - `month4.html`
     - `month5.html`
     - `consolidated.html`
     - `profit-distribution.html`
     - `style.css`
     - `script.js`

2. **التحقق من الرفع:**
   - تأكد من أن جميع الملفات في المجلد الجذر
   - تحقق من أن أذونات الملفات صحيحة (644 للملفات)

3. **الوصول للموقع:**
   - افتح `yourdomain.com/index.html`
   - أو `yourdomain.com` إذا كان `index.html` هو الملف الافتراضي

### متطلبات الاستضافة
- **استضافة ويب أساسية** (HTML/CSS/JS فقط)
- **لا يتطلب قواعد بيانات**
- **لا يتطلب PHP أو أي لغة خادم**
- **متوافق مع جميع خطط Hostinger**

## الاستخدام

### التنقل
- ابدأ من الصفحة الرئيسية (`index.html`)
- اختر التقرير المطلوب من البطاقات
- استخدم أزرار التنقل للانتقال بين التقارير

### الطباعة
- اضغط على زر "طباعة" في أي تقرير
- التقارير محسنة للطباعة بتنسيق احترافي
- تتضمن شعار الشركة وتاريخ الطباعة

### الهواتف المحمولة
- التصميم متجاوب بالكامل
- الجداول قابلة للتمرير أفقياً على الشاشات الصغيرة
- قوائم التنقل محسنة للمس

## التخصيص

### تغيير البيانات المالية
1. افتح الملف المطلوب (مثل `month3.html`)
2. ابحث عن الجدول المالي
3. عدل القيم في خانات `<td class="currency">`
4. احفظ الملف

### تغيير الألوان
1. افتح `style.css`
2. ابحث عن متغيرات الألوان
3. عدل القيم حسب الحاجة

### إضافة شهور جديدة
1. انسخ أحد ملفات الشهور (مثل `month3.html`)
2. عدل العنوان والبيانات
3. أضف رابط في `index.html`

## الدعم التقني

### المتطلبات
- متصفح حديث يدعم CSS3 و HTML5
- اتصال إنترنت لتحميل خط IBM Plex Sans Arabic
- JavaScript مفعل للمميزات التفاعلية

### استكشاف الأخطاء
- **الخط لا يظهر:** تحقق من اتصال الإنترنت
- **التنسيق مكسور:** تأكد من رفع `style.css`
- **الأزرار لا تعمل:** تأكد من رفع `script.js`

## الترخيص

هذا المشروع مطور خصيصاً لشركة E-COM
© **٢٠٢٥** شركة E-COM - جميع الحقوق محفوظة

## معلومات الاتصال

للدعم التقني أو التعديلات:
- الشركة: E-COM
- الموقع: https://ecom-eg.net

---

**ملاحظة:** هذا النظام مصمم للعمل على الاستضافة المشتركة بدون الحاجة لقواعد بيانات أو تقنيات خادم متقدمة.
