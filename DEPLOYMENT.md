# دليل النشر على Hostinger

## الملفات المطلوبة للنشر

تأكد من رفع الملفات التالية إلى مجلد `public_html` في حساب Hostinger:

### الملفات الأساسية
```
✅ index.html              - الصفحة الرئيسية
✅ month3.html            - تقرير الشهر الثالث  
✅ month4.html            - تقرير الشهر الرابع
✅ month5.html            - تقرير الشهر الخامس
✅ consolidated.html      - التقرير الموحد
✅ profit-distribution.html - توزيع الأرباح
✅ style.css              - ملف التنسيق
✅ script.js              - ملف JavaScript
```

## خطوات النشر

### 1. تسجيل الدخول إلى Hostinger
- ادخل إلى لوحة تحكم Hostinger
- اختر "File Manager" أو "مدير الملفات"

### 2. الانتقال إلى المجلد الصحيح
- انتقل إلى مجلد `public_html`
- هذا هو المجلد الذي يحتوي على ملفات الموقع

### 3. رفع الملفات
- اختر "Upload" أو "رفع"
- ارفع جميع الملفات الـ 8 المذكورة أعلاه
- تأكد من أن الملفات في المجلد الجذر وليس في مجلد فرعي

### 4. التحقق من الأذونات
- تأكد من أن أذونات الملفات هي 644
- إذا لم تكن كذلك، غيرها من خلال "Properties" أو "خصائص"

### 5. اختبار الموقع
- افتح `yourdomain.com` في المتصفح
- تأكد من ظهور الصفحة الرئيسية بشكل صحيح
- اختبر جميع الروابط والتقارير

## التحقق من النجاح

### ✅ قائمة التحقق
- [ ] الصفحة الرئيسية تظهر بشكل صحيح
- [ ] شعار E-COM يظهر
- [ ] الخط العربي يظهر بشكل صحيح
- [ ] جميع الروابط تعمل
- [ ] التقارير المالية تظهر بالتنسيق الصحيح
- [ ] أزرار الطباعة تعمل
- [ ] الموقع متجاوب على الهاتف

### 🔧 حل المشاكل الشائعة

**المشكلة:** الخط العربي لا يظهر
**الحل:** تأكد من وجود اتصال إنترنت، الخط يُحمل من Google Fonts

**المشكلة:** التنسيق مكسور
**الحل:** تأكد من رفع ملف `style.css` في نفس المجلد

**المشكلة:** الأزرار لا تعمل
**الحل:** تأكد من رفع ملف `script.js`

**المشكلة:** الصور لا تظهر
**الحل:** شعار E-COM يُحمل من الإنترنت، تأكد من الاتصال

## معلومات تقنية

### المتطلبات
- ✅ استضافة ويب أساسية (HTML/CSS/JS)
- ✅ لا يتطلب قاعدة بيانات
- ✅ لا يتطلب PHP أو أي لغة خادم
- ✅ متوافق مع جميع خطط Hostinger

### الأداء
- حجم الملفات: أقل من 1 ميجابايت
- سرعة التحميل: سريع جداً
- استهلاك الموارد: منخفض جداً

## الصيانة

### التحديثات الدورية
- لا توجد تحديثات مطلوبة
- النظام يعمل بشكل مستقل
- يمكن تعديل البيانات المالية حسب الحاجة

### النسخ الاحتياطي
- احتفظ بنسخة من جميع الملفات
- يمكن إعادة رفعها في أي وقت
- لا توجد قاعدة بيانات للنسخ الاحتياطي

## الدعم

للمساعدة في النشر أو التعديلات:
- راجع ملف README.md للتفاصيل التقنية
- تأكد من اتباع جميع الخطوات بالترتيب
- اختبر الموقع بعد كل خطوة

---

**ملاحظة مهمة:** هذا النظام مصمم خصيصاً للعمل على الاستضافة المشتركة بدون تعقيدات تقنية.
