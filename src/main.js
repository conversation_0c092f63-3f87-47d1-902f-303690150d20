import './style.css'

// E-COM Financial Reports Application
// This file handles any interactive functionality for the financial reports

// Format currency numbers with commas
function formatCurrency(amount) {
  return new Intl.NumberFormat('ar-EG', {
    style: 'currency',
    currency: 'EGP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}

// Print functionality
function setupPrintButtons() {
  const printButtons = document.querySelectorAll('button[onclick="window.print()"]');
  printButtons.forEach(button => {
    button.addEventListener('click', () => {
      window.print();
    });
  });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  setupPrintButtons();

  // Add any additional interactive features here
  console.log('E-COM Financial Reports System Loaded');
});
