@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'IBM Plex Sans Arabic', sans-serif;
  margin: 0;
  padding: 0;
  background-color: #ffffff;
  color: #000000;
  direction: rtl;
  text-align: right;
}

/* Print Styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }

  .no-print {
    display: none !important;
  }

  .print-break {
    page-break-before: always;
  }

  table {
    border-collapse: collapse !important;
  }

  th, td {
    border: 1px solid #000 !important;
    padding: 8px !important;
  }
}

/* Custom Components */
@layer components {
  .financial-table {
    @apply w-full border-collapse border border-black;
  }

  .financial-table th {
    @apply bg-gray-100 border border-black px-4 py-2 text-right font-semibold;
  }

  .financial-table td {
    @apply border border-black px-4 py-2 text-right;
  }

  .financial-table .total-row {
    @apply bg-gray-50 font-semibold;
  }

  .currency {
    @apply font-mono;
  }

  .nav-button {
    @apply bg-black text-white px-4 py-2 rounded hover:bg-gray-800 transition-colors;
  }

  .nav-button.active {
    @apply bg-gray-600;
  }
}
