@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'IBM Plex Sans Arabic', sans-serif;
  background-color: #ffffff;
  color: #000000;
  direction: rtl;
  text-align: right;
  line-height: 1.6;
}

/* Layout Classes */
.min-h-screen {
  min-height: 100vh;
}

.max-w-7xl {
  max-width: 80rem;
  margin: 0 auto;
}

.max-w-6xl {
  max-width: 72rem;
  margin: 0 auto;
}

.container {
  width: 100%;
  padding: 0 1rem;
}

/* Flexbox Utilities */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.space-x-4 > * + * {
  margin-right: 1rem;
}

.space-x-reverse {
  flex-direction: row-reverse;
}

/* Grid System */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

/* Spacing */
.p-6 {
  padding: 1.5rem;
}

.p-4 {
  padding: 1rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pr-4 {
  padding-right: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-12 {
  margin-top: 3rem;
}

/* Typography */
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

/* Colors */
.text-black {
  color: #000000;
}

.text-white {
  color: #ffffff;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-green-600 {
  color: #059669;
}

.text-blue-600 {
  color: #2563eb;
}

.text-red-600 {
  color: #dc2626;
}

.bg-white {
  background-color: #ffffff;
}

.bg-black {
  background-color: #000000;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.bg-red-50 {
  background-color: #fef2f2;
}

.bg-green-50 {
  background-color: #f0fdf4;
}

.bg-green-100 {
  background-color: #dcfce7;
}

/* Borders */
.border {
  border-width: 1px;
  border-style: solid;
  border-color: #000000;
}

.border-2 {
  border-width: 2px;
  border-style: solid;
  border-color: #000000;
}

.border-b-2 {
  border-bottom-width: 2px;
  border-bottom-style: solid;
  border-bottom-color: #000000;
}

.border-t-2 {
  border-top-width: 2px;
  border-top-style: solid;
  border-top-color: #000000;
}

.border-black {
  border-color: #000000;
}

.border-green-500 {
  border-color: #10b981;
}

.rounded-lg {
  border-radius: 0.5rem;
}

/* Images */
.h-16 {
  height: 4rem;
}

.h-20 {
  height: 5rem;
}

.w-auto {
  width: auto;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Tables */
.financial-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #000000;
  margin: 1rem 0;
}

.financial-table th {
  background-color: #f3f4f6;
  border: 1px solid #000000;
  padding: 0.75rem 1rem;
  text-align: right;
  font-weight: 600;
}

.financial-table td {
  border: 1px solid #000000;
  padding: 0.75rem 1rem;
  text-align: right;
}

.financial-table .total-row {
  background-color: #f9fafb;
  font-weight: 600;
}

.w-3-4 {
  width: 75%;
}

.w-1-4 {
  width: 25%;
}

.w-2-5 {
  width: 40%;
}

.w-1-6 {
  width: 16.666667%;
}

.w-1-5 {
  width: 20%;
}

/* Currency formatting */
.currency {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* Buttons */
.nav-button {
  background-color: #000000;
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  text-decoration: none;
  display: inline-block;
  transition: background-color 0.3s ease;
  border: none;
  cursor: pointer;
  font-family: inherit;
}

.nav-button:hover {
  background-color: #374151;
}

.nav-button.active {
  background-color: #4b5563;
}

/* Responsive Design */
@media (min-width: 768px) {
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* Overflow handling */
.overflow-x-auto {
  overflow-x: auto;
}

/* Shadows */
.hover\\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.transition-shadow {
  transition: box-shadow 0.3s ease;
}

.transition-colors {
  transition: color 0.3s ease, background-color 0.3s ease;
}

/* Print Styles */
@media print {
  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
  }
  
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  .financial-table {
    border-collapse: collapse !important;
    width: 100% !important;
  }
  
  .financial-table th,
  .financial-table td {
    border: 1px solid #000 !important;
    padding: 8px !important;
    font-size: 10pt !important;
  }
  
  .hidden {
    display: none;
  }
  
  .print\\:block {
    display: block !important;
  }
  
  h1, h2, h3 {
    page-break-after: avoid;
  }
  
  table {
    page-break-inside: avoid;
  }
}
